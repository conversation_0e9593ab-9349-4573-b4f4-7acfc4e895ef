<template>
  <div class="dish-create">
    <NavigationBar
      :title="isEdit ? '编辑菜品' : '添加菜品'"
      left-arrow
      @click-left="onBack"
    />

    <div class="form-container">
      <Form
        ref="formRef"
        :data="formData"
        :rules="formRules"
        :align="FormType.ALIGN.RIGHT"
        :show-submit="false"
        @submit="onSubmit"
        @error="onFormError"
      >
        <!-- 菜品名称 -->
        <FormItem
          label="菜品名称"
          name="dishName"
          :value="formData.dishName"
          class="form-item"
          required
        >
          <TextField
            v-model="formData.dishName"
            placeholder="请输入菜品名称"
            :maxlength="15"
            clearable
            input-align="right"
            :is-form-item="true"
            style="padding: 0 !important;"
          />
        </FormItem>

        <!-- 菜品价格 -->
        <FormItem
          label="菜品价格"
          name="price"
          :value="formData.priceItem.dishPrice"
          class="form-item"
          required
        >
          <div class="price-input-wrapper">
            <TextField
              v-model="formData.priceItem.dishPrice"
              placeholder="请输入价格"
              type="number"
              input-align="right"
              :is-form-item="true"
              style="padding: 0 !important;"
            />
            <PriceUnitSelect v-model="formData.priceItem.priceType" />
          </div>
        </FormItem>

        <!-- 菜品图 -->
        <FormItem
          label="菜品图"
          name="image"
          :value="formData.dishResourceList"
          class="form-item"
          required
          :layout="FormType.LAYOUT.VERTICAL"
        >
          <div class="image-upload-container">
            <Upload
              v-model="formData.dishResourceList"
              :max-count="1"
              :max-size="4 * 1024 * 1024"
              :is-preview="false"
              :prohibit-operation="false"
            />
            <div class="upload-tips">建议使用高清优质图，图片不得超过4M，建议分辨率为900x900，图片比例为1:1</div>
          </div>
        </FormItem>

        <!-- 是否添加为招牌菜 -->
        <FormItem
          name="specialty"
          :value="formData.specialty"
          class="form-item"
        >
          <template #label>
            <div class="form-item-label-isSpecialty">
              <div class="form-item-label-isSpecialty-text">是否添加为招牌菜</div>
              <OnixIcon icon="info" size="16" @click="handleInfoClick" />
            </div>
          </template>
          <Switch
            v-model="formData.specialty"
            :disabled="!canSetSpecialty"
            size="24"
            @change="onSpecialtyChange"
          />
        </FormItem>
      </Form>
    </div>

    <!-- 底部按钮 -->
    <div class="footer-button">
      <Button
        type="primary"
        block
        round
        size="large"
        :disabled="!isFormValid"
        :loading="submitting"
        @click="onSubmit"
      >
        {{ isEdit ? '保存修改' : '确认添加' }}
      </Button>
    </div>
    <AlertMethod ref="alertMethodRef" />
  </div>
</template>

<script lang="ts" setup>
  import {
    ref, computed, onMounted, onBeforeUnmount
  } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { useStore } from 'vuex'
  import {
    NavigationBar,
    Button,
    TextField,
    Switch,
    showToast,
    Form,
    FormItem,
    FormType
  } from '@xhs/reds-h5-next'
  import OnixIcon from '@xhs/onix-icon'
  import Upload from './components/ImageUpload/index.vue'
  import { postUpsertMenuDish } from '~/services/edith_post_upsert_menu_dish'
  import PriceUnitSelect from './components/PriceUnitSelect.vue'
  import AlertMethod from './components/AlertMethod.vue'
  import {
    getSpecialtyDishCount,
  } from '~/services/menuManage'
  import {
    IDishList,
    SpecialtyType,
  } from '~/types/menu'
  import '~/assets/svg/info.svg'

  const router = useRouter()
  const route = useRoute()
  const store = useStore()
  const submitting = ref(false)
  const formRef = ref()
  const alertMethodRef = ref()
  // 从路由获取参数
  const groupId = computed(() => route.query.groupId as string)
  const poiId = computed(() => route.query.poiId as string)
  const isEdit = computed(() => !!route.query.isEdit)

  // 从 store 获取编辑菜品数据
  const editDishData = computed(() => store.getters['menuManage/dish'] as IDishList | null)

  const menuList = computed(() => store.getters['menuManage/menuList'])

  const allDishList = computed(() => menuList.value.flatMap(menu => menu.items))

  interface FormData {
    dishId?: string
    dishName: string
    priceItem: {
      dishPrice: string
      priceType: number
    }
    dishResourceList: any[]
    specialty: boolean
  }

  const formData = ref<FormData>({
    dishName: '',
    priceItem: {
      dishPrice: '',
      priceType: 1
    },
    dishResourceList: [],
    specialty: false
  })

  const originalFormData = ref<FormData | null>(null)
  const specialtyDishCount = ref(0)

  // 表单验证规则
  const formRules = {
    dishName: [
      {
        type: 'string',
        required: true,
        message: '请输入菜品名称'
      },
      {
        type: 'string',
        validator: (rule: any, value: string) => value.length <= 15,
        message: '菜品名称不能超过15个字符'
      }
    ],
    dishPrice: [
      {
        type: 'string',
        required: true,
        message: '请输入菜品价格'
      },
      {
        type: 'string',
        validator: (rule: any, value: string) => /^\d+(\.\d{1})?$/.test(value),
        message: '价格格式不正确，最多保留1位小数'
      },
      {
        type: 'string',
        validator: (rule: any, value: string) => parseFloat(value) > 0,
        message: '价格必须大于0'
      }
    ],
    image: [
      {
        type: 'array',
        validator: (rule: any, value: any[]) => value && value.length > 0,
        message: '请上传菜品图片'
      }
    ]
  }

  // 是否可以设置为招牌菜
  const canSetSpecialty = computed(() => {
    if (formData.value.specialty) return true // 已经是招牌菜可以取消
    return specialtyDishCount.value < 10
  })

  const handleInfoClick = () => {
    alertMethodRef.value.showAlert({
      title: '招牌菜',
      message: '开启后将会同时在「招牌菜」分组展示',
      showCancelButton: false,
      showConfirmButton: true,
      confirmText: '我知道了',
    })
  }
  // 表单验证
  const isFormValid = computed(() => formData.value.dishName.trim() !== ''
    && formData.value.priceItem.dishPrice !== ''
    && formData.value.dishResourceList.length > 0
    && parseFloat(formData.value.priceItem.dishPrice) > 0
    && /^\d+(\.\d{1})?$/.test(formData.value.priceItem.dishPrice))

  // 检测是否有变更
  const hasChanges = computed(() => {
    if (!originalFormData.value) return false
    return JSON.stringify(formData.value) !== JSON.stringify(originalFormData.value)
  })

  // 表单错误处理
  const onFormError = (errors: any, fields: any) => {
    console.error('表单验证失败:', errors, fields)
    // 显示第一个错误信息
    if (errors && errors.length > 0) {
      showToast(errors[0].message || '表单验证失败')
    }
  }

  // 招牌菜切换
  const onSpecialtyChange = (value: boolean) => {
    if (value && specialtyDishCount.value >= 10) {
      showToast('最多设置10个招牌菜')
      formData.value.specialty = false
      return
    }
    formData.value.specialty = value
  }

  // 获取招牌菜数量
  const fetchSpecialtyCount = async () => {
    try {
      specialtyDishCount.value = await getSpecialtyDishCount(poiId.value)
    } catch (error) {
      console.error('获取招牌菜数量失败:', error)
    }
  }

  // 初始化表单数据
  const initializeFormData = () => {
    if (editDishData.value) {
      // 使用传递的菜品数据
      const dish = editDishData.value
      formData.value = {
        ...formData.value,
        dishName: dish.dishName,
        priceItem: {
          dishPrice: dish.priceItem.dishPrice.toString(),
          priceType: dish.priceItem.priceType,
        },
        dishResourceList: dish.dishResourceList,
        specialty: dish.specialty === SpecialtyType.YES
      }
      originalFormData.value = JSON.parse(JSON.stringify(formData.value))
    }
  }

  const checkDishDuplicate = async ({ dishName, dishPrice, priceType }: { dishName: string; dishPrice: number; priceType: number }) => {
    if (allDishList.value.length === 0) return false
    return allDishList.value.some(dish =>
      dish.dishName === dishName
      && dish.priceItem.dishPrice === dishPrice
      && dish.priceItem.priceType === priceType)
  }

  // 提交表单
  const onSubmit = async () => {
    if (!isFormValid.value) return

    submitting.value = true

    // 菜品去重检查
    const isDuplicate = await checkDishDuplicate({
      dishName: formData.value.dishName,
      dishPrice: parseFloat(formData.value.priceItem.dishPrice),
      priceType: formData.value.priceItem.priceType
    })

    if (isDuplicate) {
      showToast('菜品重复')
      return
    }

    try {
      const dishData = {
        dishId: formData.value.dishId,
        dishName: formData.value.dishName,
        priceItem: {
          dishPrice: parseFloat(formData.value.priceItem.dishPrice),
          priceType: formData.value.priceItem.priceType
        },
        dishResourceList: formData.value.dishResourceList.map((item: any) => ({
          resourceId: item.fileId,
          type: 0,
          url: item.url,
          resourceInfo: 0,
        })) || [],
        specialty: formData.value.specialty ? 1 : 0, // 0-否，1-是
        dishSource: 0, // 0-商家上传
      }
      // 调用保存接口
      await postUpsertMenuDish({
        poiId: poiId.value,
        menuGroup: {
          groupId: groupId.value,
          dish: dishData
        }
      })

      showToast(isEdit.value ? '菜品修改成功' : '菜品已添加')
      router.back()
    } catch (error) {
      console.error('保存失败:', error)
      showToast('保存失败，请重试')
    } finally {
      submitting.value = false
    }
  }

  // 返回处理
  const onBack = async () => {
    if (hasChanges.value) {
      showToast('当前编辑菜品内容尚未保存')
      return
    }
    router.back()
  }

  // 页面离开前检查
  const beforeUnload = (e: BeforeUnloadEvent) => {
    if (hasChanges.value) {
      e.preventDefault()
      e.returnValue = ''
    }
  }

  onMounted(() => {
    fetchSpecialtyCount()
    initializeFormData()
    window.addEventListener('beforeunload', beforeUnload)
  })

  onBeforeUnmount(() => {
    window.removeEventListener('beforeunload', beforeUnload)
    // 清理 store 中的编辑数据
    store.dispatch('menuManage/clearDish')
  })
</script>

<style lang="stylus" scoped>
.dish-create
  min-height 100vh
  background-color #f7f8fa

.form-container
  padding 16px 16px 120px

  .form-item
    background white
    border-radius 12px
    padding 16px
    margin-bottom 12px

.price-input-wrapper
  display flex
  align-items center
  gap 8px

.image-upload-container
  .dish-uploader
    margin-bottom 12px

  .upload-area
    width 80px
    height 80px
    border 2px dashed #dcdee0
    border-radius 8px
    display flex
    align-items center
    justify-content center
    background #fafafa

  .upload-tips
    font-size 12px
    color rgba(0, 0, 0, 0.45)
    line-height 18px
    margin-top 12px

.specialty-section
  display flex
  align-items center
  justify-content space-between

  .specialty-header
    display flex
    align-items center
    gap 4px

.footer-button
  position fixed
  bottom 0
  left 0
  right 0
  padding 16px
  background white
  border-top 1px solid #ebedf0
  z-index 100
.form-item-label-isSpecialty
  width 200px
  display flex
  align-items center
  gap 4px
  .form-item-label-isSpecialty-text
    font-size 16px
    color rgba(0, 0, 0, 0.8)
    font-weight 400
    line-height 24px
  .form-item-label-tips
    width 16px
    height 16px
</style>
