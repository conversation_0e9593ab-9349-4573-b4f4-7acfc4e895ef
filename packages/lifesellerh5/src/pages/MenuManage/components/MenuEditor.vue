<template>
  <div>
    <NavigationBar
      title="菜单管理"
      left-arrow
      @click-left="onBack"
    />

    <div class="menu-content">
      <!-- 数据合并加载提示 -->
      <div v-if="isDataMerging" class="data-merging-tip">
        <div class="merging-content">
          <div class="merging-icon">🔄</div>
          <div class="merging-text">正在同步最新菜单数据...</div>
        </div>
      </div>

      <!-- B端菜单编辑 -->
      <div>
        <!-- 招牌菜分组 - 固定在顶部，不可拖拽 -->
        <div v-if="specialtyGroup && specialtyGroup.dishList.length > 0">
          <MenuGroup
            :group="specialtyGroup"
            :index="0"
            :total-groups="menuGroups.length"
            :specialty-dish-count="specialtyDishCount"
            @update-group="onUpdateGroup"
            @delete-group="onDeleteGroup"
            @add-dish="onAddDish"
            @edit-dish="onEditDish"
            @delete-dish="onDeleteDish"
            @sort-dishes="onSortDishes"
            @rename-group="onRenameGroup"
            @cross-group-drag="onCrossGroupDrag"
          />
        </div>

        <!-- 普通分组 - 可拖拽排序 -->
        <div>
          <draggable
            v-model="regularGroups"
            group="groups"
            item-key="groupId"
            handle=".group-drag-handle"
            @change="onRegularGroupsChange"
          >
            <template #item="{ element: group, index }">
              <div class="group-item">
                <!-- 普通分组的拖拽按钮 -->
                <OnixIcon
                  v-if="regularGroups.length > 1"
                  icon="menu_m"
                  size="24"
                  class="group-drag-handle"
                />
                <MenuGroup
                  :group="group"
                  :index="index + 1"
                  :total-groups="menuGroups.length"
                  :specialty-dish-count="specialtyDishCount"
                  @update-group="onUpdateGroup"
                  @delete-group="onDeleteGroup"
                  @add-dish="onAddDish"
                  @edit-dish="onEditDish"
                  @delete-dish="onDeleteDish"
                  @sort-dishes="onSortDishes"
                  @rename-group="onRenameGroup"
                  @cross-group-drag="onCrossGroupDrag"
                />
              </div>
            </template>
          </draggable>
        </div>

        <!-- 添加分组按钮 -->
        <div class="add-group-btn" :style="{ marginLeft: regularGroups.length > 1 ? '32px' : 0 }" @click="onAddGroup">
          <span>增加分组</span>
        </div>
      </div>
    </div>

    <!-- 底部按钮区域 -->
    <footer class="bottom-actions">
      <div class="preview-btn" @click="onPreview">
        <OnixIcon icon="eyeView" size="20" />
        <div class="preview-btn-text">预览</div>
      </div>
      <Button
        :disabled="canSubmit"
        type="primary"
        block
        round
        :loading="saving"
        @click="onSave"
      >
        保存
      </Button>
    </footer>

    <!-- 重命名弹窗 -->
    <RenameAlert
      ref="renameAlertRef"
      @confirm="onRenameConfirm"
      @cancel="onRenameCancel"
    />
    <AlertMethod ref="alertMethodRef" />
  </div>
</template>

<script lang="ts" setup>
  import {
    ref, computed, onMounted, onActivated, onBeforeUnmount, watch, nextTick
  } from 'vue'
  import { useRouter } from 'vue-router'
  import { useStore } from 'vuex'
  import {
    NavigationBar,
    Button,
    showToast,
    ToastType,
  } from '@xhs/reds-h5-next'
  import OnixIcon from '@xhs/onix-icon'
  import draggable from 'vuedraggable'
  import MenuGroup from './MenuGroup.vue'
  import RenameAlert from './RenameAlert.vue'
  import AlertMethod from './AlertMethod.vue'
  import { saveMenuData } from '~/services/menuManage'
  import {
    IData, IMenuGroupListExtended, IDishList
  } from '~/types/menu'
  import ROUTE_NAME from '~/constants/routename'

  import { postQueryMenu } from '~/services/edith_post_query_menu'
  import { postUpsertMenuGroup } from '~/services/edith_post_upsert_menu_group'

  import '~/assets/svg/menu_m.svg'
  import '~/assets/svg/eyeView.svg'

  interface Props {
    poiId?: string
  }

  const props = withDefaults(defineProps<Props>(), {
    poiId: ''
  })

  const router = useRouter()
  const store = useStore()
  const saving = ref(false)
  const menuGroups = ref<IMenuGroupListExtended[]>([])
  const renameAlertRef = ref()
  const currentRenameGroupId = ref('')
  const alertMethodRef = ref()

  // 页面激活状态管理
  const isFirstLoad = ref(true)
  const previousMenuGroups = ref<IMenuGroupListExtended[]>([])
  const isDataMerging = ref(false)

  // 招牌菜分组
  const specialtyGroup = computed(() => menuGroups.value.find(g => g.isSpecialty) || null)

  // 普通分组
  const regularGroups = computed({
    get: () => menuGroups.value.filter(g => !g.isSpecialty),
    set: (value: IMenuGroupListExtended[]) => {
      // 重新组合分组：招牌菜分组 + 普通分组
      const specialty = menuGroups.value.find(g => g.isSpecialty)
      menuGroups.value = specialty ? [specialty, ...value] : value
    }
  })

  // 招牌菜数量
  const specialtyDishCount = computed(() => {
    const specialtyGroup = menuGroups.value.find(g => g.isSpecialty)
    return specialtyGroup?.dishList.length || 0
  })

  // 是否可以提交
  // 有菜品，并且名字不等于默认名称
  const canSubmit = computed(() => menuGroups.value.length > 0 && menuGroups.value.some(group => group.dishList && group.dishList.length > 0 && group.groupName !== '菜品分组名称'))

  // 深拷贝函数
  const deepClone = (obj: any): any => {
    if (obj === null || typeof obj !== 'object') return obj
    if (obj instanceof Date) return new Date(obj.getTime())
    if (obj instanceof Array) return obj.map(item => deepClone(item))
    if (typeof obj === 'object') {
      const clonedObj: any = {}
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = deepClone(obj[key])
        }
      }
      return clonedObj
    }
    return obj
  }

  // 智能数据合并逻辑
  const mergeMenuData = (newData: IMenuGroupListExtended[], oldData: IMenuGroupListExtended[]): IMenuGroupListExtended[] => {
    if (!oldData.length) return newData

    const result: IMenuGroupListExtended[] = []
    const newGroupMap = new Map<string, IMenuGroupListExtended>()
    const newDishMap = new Map<string, IDishList>()

    // 构建新数据的映射
    newData.forEach(group => {
      newGroupMap.set(group.groupId, group)
      group.dishList.forEach(dish => {
        newDishMap.set(dish.dishId, dish)
      })
    })

    // 处理原有数据，保持原有顺序
    oldData.forEach(oldGroup => {
      const newGroup = newGroupMap.get(oldGroup.groupId)
      if (newGroup) {
        // 分组存在，合并菜品数据
        const mergedDishes: IDishList[] = []
        const newDishesInGroup = new Set(newGroup.dishList.map(d => d.dishId))

        // 保持原有菜品的顺序，更新数据
        oldGroup.dishList.forEach(oldDish => {
          const newDish = newDishMap.get(oldDish.dishId)
          if (newDish) {
            mergedDishes.push(newDish)
            newDishesInGroup.delete(oldDish.dishId)
          }
        })

        // 添加新增的菜品到分组末尾
        newGroup.dishList.forEach(newDish => {
          if (newDishesInGroup.has(newDish.dishId)) {
            mergedDishes.push(newDish)
          }
        })

        // 更新分组数据但保持原有位置
        result.push({
          ...newGroup,
          dishList: mergedDishes
        })
        newGroupMap.delete(oldGroup.groupId)
      }
    })

    // 添加新增的分组到末尾
    newData.forEach(newGroup => {
      if (newGroupMap.has(newGroup.groupId)) {
        result.push(newGroup)
      }
    })

    return result
  }

  // 获取菜单数据
  const fetchMenuData = async (shouldMerge = false) => {
    try {
      isDataMerging.value = shouldMerge

      // 真实接口调用
      const data = await postQueryMenu({ poiId: props.poiId })

      // 收集所有招牌菜
      const specialtyDishes: IDishList[] = []
      if (data.menuGroupList.length > 0) {
        // 遍历所有菜品组，提取招牌菜
        data.menuGroupList.forEach(group => {
          if (group.dishList && group.dishList.length > 0 && group.groupName !== '菜品分组名称') {
            group.dishList.forEach(dish => {
              if (dish.specialty === 1) {
                specialtyDishes.push(dish)
              }
            })
          }
        })
        // 根据 recommendSortOrder 排序招牌菜
        specialtyDishes.sort((a, b) => {
          const orderA = a.recommendSortOrder || 0
          const orderB = b.recommendSortOrder || 0
          return orderA - orderB
        })
        // 创建招牌菜分组
        const specialtyGroup: IMenuGroupListExtended = {
          groupId: 'specialty',
          groupName: '招牌菜',
          dishList: specialtyDishes,
          sortOrder: 0,
          isSpecialty: true
        }
        // 转换原始数据，添加 isSpecialty 标识（原分组保留所有菜品）
        const regularGroups = data.menuGroupList.map(group => ({
          ...group,
          isSpecialty: false
        }))

        // 组合新的菜单数据
        const newMenuData = [specialtyGroup, ...regularGroups]

        // 如果需要合并数据且不是首次加载
        if (shouldMerge && !isFirstLoad.value && previousMenuGroups.value.length > 0) {
          menuGroups.value = mergeMenuData(newMenuData, previousMenuGroups.value)
        } else {
          menuGroups.value = newMenuData
        }

        // 保存当前数据作为下次合并的基础
        previousMenuGroups.value = deepClone(menuGroups.value)
      } else if (!shouldMerge) {
        onAddGroup()
      }
    } catch (error) {
      console.error('获取菜单数据失败:', error)
      showToast('获取菜单数据失败')
    } finally {
      isDataMerging.value = false
    }
  }

  // 进入预览模式 - 跳转到预览页面
  const onPreview = () => {
    // 这里可以跳转到预览页面或者弹出预览弹窗
    showToast('预览功能开发中')
  }

  // 返回处理
  const onBack = () => {
    router.back()
  }

  // 普通分组变更
  const onRegularGroupsChange = () => {
    // 更新sortOrder
    regularGroups.value.forEach((group, index) => {
      group.sortOrder = index + 1 // 从1开始，因为招牌菜分组是0
    })
  }

  // 重命名分组
  const onRenameGroup = (groupId: string) => {
    const group = menuGroups.value.find(g => g.groupId === groupId)

    // 获取所有分组名称，过滤掉当前分组
    const allGroupNames = menuGroups.value
      .filter(g => g.groupId !== groupId)
      .map(g => g.groupName)

    renameAlertRef.value?.open({
      groupName: group?.groupName,
      allGroupNames,
      groupId,
    })
  }

  // 重命名确认
  const onRenameConfirm = async (name: string, groupId: string) => {
    console.log('onRenameConfirm', name, groupId)
    const payload = {
      groupId,
      groupName: name,
      poiId: props.poiId,
      groupCategory: 0,
    }
    try {
      await postUpsertMenuGroup(payload)
      fetchMenuData()
      showToast('重命名成功')
    } catch (error) {
      console.error('重命名失败:', error)
      showToast('重命名失败')
    }
  }

  // 重命名取消
  const onRenameCancel = () => {
    currentRenameGroupId.value = ''
  }

  // 更新分组
  const onUpdateGroup = (groupId: string, updates: Partial<IMenuGroupListExtended>) => {
    const group = menuGroups.value.find(g => g.groupId === groupId)
    if (!group) return

    Object.assign(group, updates)
  }

  // 删除分组
  const onDeleteGroup = (groupId: string) => {
    // 只有1个分组时不能删除
    const nonSpecialtyGroups = menuGroups.value.filter(g => !g.isSpecialty)
    if (nonSpecialtyGroups.length <= 1) return

    alertMethodRef.value.showAlert({
      title: '确认删除分组？',
      message: '组内菜品也会一并删除，且无法恢复，请仔细确认后再操作',
      confirmText: '确认删除',
      cancelText: '取消',
      footerLayout: 'horizontal',
      onConfirm: () => {
        const index = menuGroups.value.findIndex(g => g.groupId === groupId)
        if (index > -1) {
          menuGroups.value.splice(index, 1)
          showToast('分组已删除')
        }
      },
      onCancel: () => {}
    })
  }

  // 添加菜品
  const onAddDish = (groupId: string) => {
    router.push({
      name: ROUTE_NAME.DISH_CREATE,
      query: {
        groupId,
        poiId: props.poiId
      }
    })
  }

  // 编辑菜品
  const onEditDish = (dish: IDishList) => {
    // 将菜品数据存入 store
    store.dispatch('menuManage/setDish', dish)

    router.push({
      name: ROUTE_NAME.DISH_CREATE,
      query: {
        id: dish.dishId,
        poiId: props.poiId,
        isEdit: 'true',
        sid: 'session.1752485807395587337513'
      }
    })
  }

  // 删除菜品
  const onDeleteDish = (groupId: string, dishId: string) => {
    alertMethodRef.value.showAlert({
      title: '确认删除菜品？',
      message: '菜品删除无法恢复，请仔细确认后再操作',
      confirmText: '确认删除',
      cancelText: '取消',
      footerLayout: 'horizontal',
      onConfirm: () => {
        const group = menuGroups.value.find(g => g.groupId === groupId)
        if (group) {
          const dishIndex = group.dishList.findIndex(d => d.dishId === dishId)
          if (dishIndex > -1) {
            group.dishList.splice(dishIndex, 1)
            showToast({
              type: ToastType.ToastBuiltInType.TEXT,
              message: '菜品已删除'
            })
          }
        }
      },
      onCancel: () => {}
    })
  }

  // 菜品排序
  const onSortDishes = (groupId: string, dishes: IDishList[]) => {
    const group = menuGroups.value.find(g => g.groupId === groupId)
    if (group) {
      group.dishList = dishes
    }
  }

  // 处理跨组拖拽
  const onCrossGroupDrag = (dragInfo: {
    fromGroupId: string
    toGroupId: string
    dishId: string
    oldIndex: number
    newIndex: number
  }) => {
    const {
      fromGroupId, toGroupId, dishId, newIndex
    } = dragInfo

    // 找到源分组和目标分组
    const fromGroup = menuGroups.value.find(g => g.groupId === fromGroupId)
    const toGroup = menuGroups.value.find(g => g.groupId === toGroupId)

    if (!fromGroup || !toGroup) return

    // 不允许拖拽到招牌菜分组
    if (toGroup.isSpecialty) {
      showToast('不能将菜品拖拽到招牌菜分组')
      return
    }

    // 找到要移动的菜品
    const dishIndex = fromGroup.dishList.findIndex(d => d.dishId === dishId)
    if (dishIndex === -1) return

    const dish = fromGroup.dishList[dishIndex]

    // 如果是从招牌菜分组拖出，需要取消specialty标识
    if (fromGroup.isSpecialty) {
      dish.specialty = 0
      dish.recommendSortOrder = undefined
    }

    // 从源分组移除菜品
    fromGroup.dishList.splice(dishIndex, 1)

    // 添加到目标分组
    toGroup.dishList.splice(newIndex, 0, dish)

    // 更新招牌菜分组（如果从招牌菜分组拖出，需要重新收集招牌菜）
    if (fromGroup.isSpecialty) {
      const specialtyDishes: IDishList[] = []
      menuGroups.value.forEach(group => {
        if (!group.isSpecialty) {
          group.dishList.forEach(dish => {
            if (dish.specialty === 1) {
              specialtyDishes.push(dish)
            }
          })
        }
      })

      // 按 recommendSortOrder 排序
      specialtyDishes.sort((a, b) => {
        const orderA = a.recommendSortOrder || 0
        const orderB = b.recommendSortOrder || 0
        return orderA - orderB
      })

      fromGroup.dishList = specialtyDishes
    }

    showToast('菜品已移动')
  }

  // 添加分组
  const onAddGroup = async () => {
    const payload = {
      groupName: '菜品分组名称',
      poiId: props.poiId,
      groupCategory: 0,
    }

    try {
      await postUpsertMenuGroup(payload)
      showToast('添加分组成功')
      fetchMenuData()
    } catch (error) {
      console.error('添加分组失败:', error)
      showToast('添加分组失败')
    }
  }

  // 保存菜单
  const onSave = async () => {
    saving.value = true
    try {
      const menuData: IData = {
        menuGroupList: menuGroups.value.map(group => ({
          groupId: group.groupId,
          groupName: group.groupName,
          dishList: group.dishList,
          sortOrder: group.sortOrder,
          groupStatus: group.groupStatus
        }))
      }

      await saveMenuData(props.poiId, menuData)
      showToast('菜单已保存')
    } catch (error) {
      console.error('保存失败:', error)
      showToast('保存失败，请重试')
    } finally {
      saving.value = false
    }
  }

  // 防抖处理的页面激活逻辑
  let activationTimer: any = null

  // 页面激活时的数据刷新逻辑
  const handlePageActivation = async () => {
    if (!isFirstLoad.value && !isDataMerging.value) {
      // 清除之前的定时器
      if (activationTimer) {
        clearTimeout(activationTimer)
      }

      // 防抖处理，避免频繁调用
      activationTimer = setTimeout(async () => {
        try {
          // 非首次加载时，进行智能数据合并
          await fetchMenuData(true)
        } catch (error) {
          console.error('页面激活时获取数据失败:', error)
        }
      }, 300) // 300ms防抖
    }
  }

  // 页面可见性变化处理
  const handleVisibilityChange = async () => {
    if (!document.hidden && !isFirstLoad.value) {
      // 页面从隐藏变为可见，且不是首次加载
      await handlePageActivation()
    }
  }

  // 监听页面激活事件（用于keep-alive组件）
  onActivated(handlePageActivation)

  // 监听页面可见性变化（用于处理浏览器标签页切换、router.back()等情况）
  watch(() => router.currentRoute.value.fullPath, async (newPath, oldPath) => {
    if (newPath && oldPath && newPath !== oldPath) {
      // 路由发生了真实变化，重置首次加载标识
      await nextTick()
      if (!isFirstLoad.value) {
        await handlePageActivation()
      }
    }
  })

  // 窗口焦点变化处理（用于检测router.back()等情况）
  const handleWindowFocus = async () => {
    if (!isFirstLoad.value) {
      await handlePageActivation()
    }
  }

  onMounted(async () => {
    await fetchMenuData()
    isFirstLoad.value = false

    // 添加页面可见性变化监听
    document.addEventListener('visibilitychange', handleVisibilityChange)
    // 添加窗口焦点监听（用于检测页面重新激活）
    window.addEventListener('focus', handleWindowFocus)
  })

  onBeforeUnmount(() => {
    // 清理定时器
    if (activationTimer) {
      clearTimeout(activationTimer)
    }

    // 清理事件监听器
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    window.removeEventListener('focus', handleWindowFocus)
  })
</script>

<style lang="stylus" scoped>
.nav-actions
  .action-btn
    font-size 14px
    color #1890ff
    cursor pointer

.menu-content
  padding-bottom 140px

.bottom-actions
  position fixed
  bottom 0
  left 0
  right 0
  padding 16px
  background white
  border-top 1px solid #f0f0f0
  display flex
  justify-content center
  align-items center
  gap 16px
  @supports (padding-bottom: env(safe-area-inset-bottom, 16px)) {
      padding-bottom: env(safe-area-inset-bottom, 16px);
    }
  @supports (padding-bottom: constant(safe-area-inset-bottom, 16px)) {
    padding-bottom: constant(safe-area-inset-bottom, 16px);
  }

.preview-btn
  display flex
  flex-direction column
  align-items center
  justify-content center
  color rgba(0, 0, 0, 0.62)
  .preview-btn-text
    color rgba(0, 0, 0, 0.62)
    font-size 10px
    line-height 14px

.add-group-btn
  display flex
  align-items center
  justify-content center
  height 44px
  background rgba(255, 255, 255, 1)
  border-radius 12px
  color rgba(0, 0, 0, 0.62)
  font-size 16px
  font-weight 500
  line-height 24px

.group-item
  display flex
  align-items flex-start
  gap 8px
  .group-drag-handle
    color rgba(0, 0, 0, 0.45)
    margin-top 10px

.data-merging-tip
  position fixed
  top 0
  left 0
  right 0
  bottom 0
  background rgba(0, 0, 0, 0.3)
  display flex
  align-items center
  justify-content center
  z-index 1000

  .merging-content
    background white
    border-radius 12px
    padding 24px
    display flex
    flex-direction column
    align-items center
    gap 12px
    box-shadow 0 4px 12px rgba(0, 0, 0, 0.15)

    .merging-icon
      font-size 24px
      animation rotate 1s linear infinite

    .merging-text
      font-size 16px
      color rgba(0, 0, 0, 0.85)
      font-weight 500

@keyframes rotate
  from
    transform rotate(0deg)
  to
    transform rotate(360deg)
</style>
