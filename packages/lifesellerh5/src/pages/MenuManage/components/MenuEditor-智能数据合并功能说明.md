# MenuEditor.vue 智能数据合并功能说明

## 功能概述

在MenuEditor.vue组件中实现了智能数据合并功能，当页面通过router.back()重新展示时，自动调用fetchMenuData()获取最新的菜单数据，并实现智能数据合并逻辑。

## 主要功能

### 1. 数据获取时机

- **页面激活监听**: 使用Vue的`onActivated`生命周期钩子监听keep-alive组件的激活
- **页面可见性监听**: 使用`visibilitychange`事件监听浏览器标签页切换
- **窗口焦点监听**: 使用`focus`事件监听窗口重新获得焦点
- **路由变化监听**: 使用Vue Router的watch监听路由变化

### 2. 数据合并策略

#### 核心原则
- **保留原有数据的排序顺序**: dish和group的原始位置不变
- **新增数据插入规则**: 
  - 新增的dish：插入到对应group的最后位置
  - 新增的group：插入到整个菜单列表的最后位置
- **已存在数据更新**: 更新其数据内容但保持原有位置

#### 实现逻辑
```javascript
const mergeMenuData = (newData, oldData) => {
  // 1. 构建新数据的映射表
  // 2. 遍历原有数据，保持原有顺序
  // 3. 对于存在的分组，合并菜品数据
  // 4. 对于新增的分组，添加到末尾
  // 5. 返回合并后的数据
}
```

### 3. 实现要求

#### 唯一标识符判断
- **Group**: 使用`groupId`作为唯一标识符
- **Dish**: 使用`dishId`作为唯一标识符

#### UI状态保持
- 不会破坏现有的UI状态（如展开/折叠状态）
- 通过深拷贝保存历史数据作为合并基础

#### 异步数据加载错误处理
- 使用try-catch包装所有异步操作
- 提供用户友好的错误提示
- 防抖处理避免频繁调用

### 4. 用户体验优化

#### 加载状态指示
- 显示"正在同步最新菜单数据..."的加载提示
- 带有旋转动画的视觉反馈
- 半透明遮罩层防止用户操作

#### 防抖处理
- 300ms防抖延迟，避免频繁触发数据获取
- 清理机制防止内存泄漏

## 技术实现细节

### 状态管理
```javascript
const isFirstLoad = ref(true)           // 是否首次加载
const previousMenuGroups = ref([])      // 上次的菜单数据
const isDataMerging = ref(false)        // 是否正在合并数据
```

### 生命周期钩子
```javascript
onMounted()         // 初始化数据加载
onActivated()       // keep-alive组件激活
onBeforeUnmount()   // 清理事件监听器和定时器
```

### 事件监听
```javascript
document.addEventListener('visibilitychange', handleVisibilityChange)
window.addEventListener('focus', handleWindowFocus)
```

## 使用场景

1. **用户从菜品编辑页面返回**: 通过router.back()返回菜单管理页面
2. **浏览器标签页切换**: 用户切换到其他标签页后再回来
3. **应用后台切换**: 移动端应用切换到后台后重新激活
4. **网络重连**: 网络断开重连后的数据同步

## 注意事项

1. **首次加载**: 首次加载时不进行数据合并，直接使用新数据
2. **数据一致性**: 合并过程中保持数据的一致性和完整性
3. **性能优化**: 使用防抖和状态检查避免不必要的API调用
4. **错误恢复**: 合并失败时不影响现有数据的显示

## 测试建议

1. **功能测试**: 验证各种页面激活场景下的数据合并效果
2. **性能测试**: 确保频繁切换不会造成性能问题
3. **边界测试**: 测试空数据、网络错误等边界情况
4. **用户体验测试**: 验证加载提示和交互流畅性
