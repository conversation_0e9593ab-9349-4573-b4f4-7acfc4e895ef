<template>
  <div class="menu-manage-edit">
    <MenuEditor :poi-id="poiId" />
  </div>
</template>

<script lang="ts" setup>
  import { computed } from 'vue'
  import { useRoute } from 'vue-router'
  import MenuEditor from './components/MenuEditor.vue'

  const route = useRoute()

  const poiId = computed(() => route.query.poiId as string || '')
</script>
<style lang="stylus" scoped>
.menu-manage-edit
  padding 16px
  background-color #f5f5f5
  height 100vh
</style>
